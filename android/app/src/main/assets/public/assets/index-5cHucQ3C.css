/* Custom styles on top of Pico CSS */

/* Button grid styling for equal widths */
.grid button {
  flex: 1;
}

/* API Key input styling */
.api-key-input {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.api-key-input input {
  flex: 1;
  margin-bottom: 0;
}

.api-key-input button {
  margin-bottom: 0;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  white-space: nowrap;
}

/* Output area styling */
.output-area {
  min-height: 200px;
  padding: 1rem;
  background-color: var(--pico-card-background-color);
  border: var(--pico-border-width) solid var(--pico-border-color);
  border-radius: var(--pico-border-radius);
  font-family: var(--pico-font-family);
  font-size: 0.875rem;
  line-height: 1.6;
  overflow-y: auto;
  margin-bottom: 1rem;
  color: var(--pico-color);
}

/* Markdown styling within output area */
.output-area h1,
.output-area h2,
.output-area h3,
.output-area h4,
.output-area h5,
.output-area h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.output-area h1 { font-size: 1.5rem; }
.output-area h2 { font-size: 1.25rem; }
.output-area h3 { font-size: 1.125rem; }
.output-area h4 { font-size: 1rem; }
.output-area h5 { font-size: 0.875rem; }
.output-area h6 { font-size: 0.75rem; }

.output-area p {
  margin-bottom: 1rem;
}

.output-area ul,
.output-area ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.output-area li {
  margin-bottom: 0.25rem;
}

.output-area blockquote {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid var(--pico-primary-background);
  background-color: var(--pico-muted-color);
  font-style: italic;
}

.output-area code {
  background-color: var(--pico-muted-color);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: var(--pico-font-family-monospace);
  font-size: 0.8rem;
}

.output-area pre {
  background-color: var(--pico-muted-color);
  padding: 1rem;
  border-radius: var(--pico-border-radius);
  overflow-x: auto;
  margin: 1rem 0;
}

.output-area pre code {
  background: none;
  padding: 0;
}

.output-area table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.output-area th,
.output-area td {
  padding: 0.5rem;
  border: 1px solid var(--pico-border-color);
  text-align: left;
}

.output-area th {
  background-color: var(--pico-muted-color);
  font-weight: 600;
}

.output-area strong {
  font-weight: 600;
}

.output-area em {
  font-style: italic;
}

.output-area a {
  color: var(--pico-primary-background);
  text-decoration: underline;
}

.output-area hr {
  margin: 1.5rem 0;
  border: none;
  border-top: 1px solid var(--pico-border-color);
}

/* Button icon spacing */
button i {
  width: 1rem;
  height: 1rem;
}

/* Status message styling */
#status {
  padding: 0.75rem 1rem;
  border-radius: var(--pico-border-radius);
  font-weight: 500;
  text-align: center;
  margin-top: 1rem;
  transition: all 0.2s ease-in-out;
}

#status.success {
  background-color: var(--pico-ins-color);
  color: var(--pico-contrast-inverse);
}

#status.error {
  background-color: var(--pico-del-color);
  color: var(--pico-contrast-inverse);
}

#status.info {
  background-color: var(--pico-primary-background);
  color: var(--pico-primary-inverse);
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--pico-muted-border-color);
  border-radius: 50%;
  border-top-color: var(--pico-primary-background);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
