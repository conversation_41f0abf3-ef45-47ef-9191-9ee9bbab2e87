(function polyfill() {
  const relList = document.createElement("link").relList;
  if (relList && relList.supports && relList.supports("modulepreload")) {
    return;
  }
  for (const link of document.querySelectorAll('link[rel="modulepreload"]')) {
    processPreload(link);
  }
  new MutationObserver((mutations) => {
    for (const mutation of mutations) {
      if (mutation.type !== "childList") {
        continue;
      }
      for (const node of mutation.addedNodes) {
        if (node.tagName === "LINK" && node.rel === "modulepreload")
          processPreload(node);
      }
    }
  }).observe(document, { childList: true, subtree: true });
  function getFetchOpts(link) {
    const fetchOpts = {};
    if (link.integrity) fetchOpts.integrity = link.integrity;
    if (link.referrerPolicy) fetchOpts.referrerPolicy = link.referrerPolicy;
    if (link.crossOrigin === "use-credentials")
      fetchOpts.credentials = "include";
    else if (link.crossOrigin === "anonymous") fetchOpts.credentials = "omit";
    else fetchOpts.credentials = "same-origin";
    return fetchOpts;
  }
  function processPreload(link) {
    if (link.ep)
      return;
    link.ep = true;
    const fetchOpts = getFetchOpts(link);
    fetch(link.href, fetchOpts);
  }
})();
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var ExceptionCode;
(function(ExceptionCode2) {
  ExceptionCode2["Unimplemented"] = "UNIMPLEMENTED";
  ExceptionCode2["Unavailable"] = "UNAVAILABLE";
})(ExceptionCode || (ExceptionCode = {}));
class CapacitorException extends Error {
  constructor(message, code, data) {
    super(message);
    this.message = message;
    this.code = code;
    this.data = data;
  }
}
const getPlatformId = (win) => {
  var _a, _b;
  if (win === null || win === void 0 ? void 0 : win.androidBridge) {
    return "android";
  } else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {
    return "ios";
  } else {
    return "web";
  }
};
const createCapacitor = (win) => {
  const capCustomPlatform = win.CapacitorCustomPlatform || null;
  const cap = win.Capacitor || {};
  const Plugins = cap.Plugins = cap.Plugins || {};
  const getPlatform = () => {
    return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);
  };
  const isNativePlatform = () => getPlatform() !== "web";
  const isPluginAvailable = (pluginName) => {
    const plugin = registeredPlugins.get(pluginName);
    if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {
      return true;
    }
    if (getPluginHeader(pluginName)) {
      return true;
    }
    return false;
  };
  const getPluginHeader = (pluginName) => {
    var _a;
    return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName);
  };
  const handleError = (err) => win.console.error(err);
  const registeredPlugins = /* @__PURE__ */ new Map();
  const registerPlugin2 = (pluginName, jsImplementations = {}) => {
    const registeredPlugin = registeredPlugins.get(pluginName);
    if (registeredPlugin) {
      console.warn(`Capacitor plugin "${pluginName}" already registered. Cannot register plugins twice.`);
      return registeredPlugin.proxy;
    }
    const platform = getPlatform();
    const pluginHeader = getPluginHeader(pluginName);
    let jsImplementation;
    const loadPluginImplementation = async () => {
      if (!jsImplementation && platform in jsImplementations) {
        jsImplementation = typeof jsImplementations[platform] === "function" ? jsImplementation = await jsImplementations[platform]() : jsImplementation = jsImplementations[platform];
      } else if (capCustomPlatform !== null && !jsImplementation && "web" in jsImplementations) {
        jsImplementation = typeof jsImplementations["web"] === "function" ? jsImplementation = await jsImplementations["web"]() : jsImplementation = jsImplementations["web"];
      }
      return jsImplementation;
    };
    const createPluginMethod = (impl, prop) => {
      var _a, _b;
      if (pluginHeader) {
        const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);
        if (methodHeader) {
          if (methodHeader.rtype === "promise") {
            return (options) => cap.nativePromise(pluginName, prop.toString(), options);
          } else {
            return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);
          }
        } else if (impl) {
          return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);
        }
      } else if (impl) {
        return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);
      } else {
        throw new CapacitorException(`"${pluginName}" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);
      }
    };
    const createPluginMethodWrapper = (prop) => {
      let remove;
      const wrapper = (...args) => {
        const p = loadPluginImplementation().then((impl) => {
          const fn = createPluginMethod(impl, prop);
          if (fn) {
            const p2 = fn(...args);
            remove = p2 === null || p2 === void 0 ? void 0 : p2.remove;
            return p2;
          } else {
            throw new CapacitorException(`"${pluginName}.${prop}()" is not implemented on ${platform}`, ExceptionCode.Unimplemented);
          }
        });
        if (prop === "addListener") {
          p.remove = async () => remove();
        }
        return p;
      };
      wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;
      Object.defineProperty(wrapper, "name", {
        value: prop,
        writable: false,
        configurable: false
      });
      return wrapper;
    };
    const addListener = createPluginMethodWrapper("addListener");
    const removeListener = createPluginMethodWrapper("removeListener");
    const addListenerNative = (eventName, callback) => {
      const call = addListener({ eventName }, callback);
      const remove = async () => {
        const callbackId = await call;
        removeListener({
          eventName,
          callbackId
        }, callback);
      };
      const p = new Promise((resolve) => call.then(() => resolve({ remove })));
      p.remove = async () => {
        console.warn(`Using addListener() without 'await' is deprecated.`);
        await remove();
      };
      return p;
    };
    const proxy = new Proxy({}, {
      get(_, prop) {
        switch (prop) {
          case "$$typeof":
            return void 0;
          case "toJSON":
            return () => ({});
          case "addListener":
            return pluginHeader ? addListenerNative : addListener;
          case "removeListener":
            return removeListener;
          default:
            return createPluginMethodWrapper(prop);
        }
      }
    });
    Plugins[pluginName] = proxy;
    registeredPlugins.set(pluginName, {
      name: pluginName,
      proxy,
      platforms: /* @__PURE__ */ new Set([...Object.keys(jsImplementations), ...pluginHeader ? [platform] : []])
    });
    return proxy;
  };
  if (!cap.convertFileSrc) {
    cap.convertFileSrc = (filePath) => filePath;
  }
  cap.getPlatform = getPlatform;
  cap.handleError = handleError;
  cap.isNativePlatform = isNativePlatform;
  cap.isPluginAvailable = isPluginAvailable;
  cap.registerPlugin = registerPlugin2;
  cap.Exception = CapacitorException;
  cap.DEBUG = !!cap.DEBUG;
  cap.isLoggingEnabled = !!cap.isLoggingEnabled;
  return cap;
};
const initCapacitorGlobal = (win) => win.Capacitor = createCapacitor(win);
const Capacitor = /* @__PURE__ */ initCapacitorGlobal(typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
const registerPlugin = Capacitor.registerPlugin;
class WebPlugin {
  constructor() {
    this.listeners = {};
    this.retainedEventArguments = {};
    this.windowListeners = {};
  }
  addListener(eventName, listenerFunc) {
    let firstListener = false;
    const listeners = this.listeners[eventName];
    if (!listeners) {
      this.listeners[eventName] = [];
      firstListener = true;
    }
    this.listeners[eventName].push(listenerFunc);
    const windowListener = this.windowListeners[eventName];
    if (windowListener && !windowListener.registered) {
      this.addWindowListener(windowListener);
    }
    if (firstListener) {
      this.sendRetainedArgumentsForEvent(eventName);
    }
    const remove = async () => this.removeListener(eventName, listenerFunc);
    const p = Promise.resolve({ remove });
    return p;
  }
  async removeAllListeners() {
    this.listeners = {};
    for (const listener in this.windowListeners) {
      this.removeWindowListener(this.windowListeners[listener]);
    }
    this.windowListeners = {};
  }
  notifyListeners(eventName, data, retainUntilConsumed) {
    const listeners = this.listeners[eventName];
    if (!listeners) {
      if (retainUntilConsumed) {
        let args = this.retainedEventArguments[eventName];
        if (!args) {
          args = [];
        }
        args.push(data);
        this.retainedEventArguments[eventName] = args;
      }
      return;
    }
    listeners.forEach((listener) => listener(data));
  }
  hasListeners(eventName) {
    var _a;
    return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);
  }
  registerWindowListener(windowEventName, pluginEventName) {
    this.windowListeners[pluginEventName] = {
      registered: false,
      windowEventName,
      pluginEventName,
      handler: (event) => {
        this.notifyListeners(pluginEventName, event);
      }
    };
  }
  unimplemented(msg = "not implemented") {
    return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);
  }
  unavailable(msg = "not available") {
    return new Capacitor.Exception(msg, ExceptionCode.Unavailable);
  }
  async removeListener(eventName, listenerFunc) {
    const listeners = this.listeners[eventName];
    if (!listeners) {
      return;
    }
    const index = listeners.indexOf(listenerFunc);
    this.listeners[eventName].splice(index, 1);
    if (!this.listeners[eventName].length) {
      this.removeWindowListener(this.windowListeners[eventName]);
    }
  }
  addWindowListener(handle) {
    window.addEventListener(handle.windowEventName, handle.handler);
    handle.registered = true;
  }
  removeWindowListener(handle) {
    if (!handle) {
      return;
    }
    window.removeEventListener(handle.windowEventName, handle.handler);
    handle.registered = false;
  }
  sendRetainedArgumentsForEvent(eventName) {
    const args = this.retainedEventArguments[eventName];
    if (!args) {
      return;
    }
    delete this.retainedEventArguments[eventName];
    args.forEach((arg) => {
      this.notifyListeners(eventName, arg);
    });
  }
}
const encode = (str) => encodeURIComponent(str).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);
const decode = (str) => str.replace(/(%[\dA-F]{2})+/gi, decodeURIComponent);
class CapacitorCookiesPluginWeb extends WebPlugin {
  async getCookies() {
    const cookies = document.cookie;
    const cookieMap = {};
    cookies.split(";").forEach((cookie) => {
      if (cookie.length <= 0)
        return;
      let [key, value] = cookie.replace(/=/, "CAP_COOKIE").split("CAP_COOKIE");
      key = decode(key).trim();
      value = decode(value).trim();
      cookieMap[key] = value;
    });
    return cookieMap;
  }
  async setCookie(options) {
    try {
      const encodedKey = encode(options.key);
      const encodedValue = encode(options.value);
      const expires = `; expires=${(options.expires || "").replace("expires=", "")}`;
      const path = (options.path || "/").replace("path=", "");
      const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : "";
      document.cookie = `${encodedKey}=${encodedValue || ""}${expires}; path=${path}; ${domain};`;
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async deleteCookie(options) {
    try {
      document.cookie = `${options.key}=; Max-Age=0`;
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async clearCookies() {
    try {
      const cookies = document.cookie.split(";") || [];
      for (const cookie of cookies) {
        document.cookie = cookie.replace(/^ +/, "").replace(/=.*/, `=;expires=${(/* @__PURE__ */ new Date()).toUTCString()};path=/`);
      }
    } catch (error) {
      return Promise.reject(error);
    }
  }
  async clearAllCookies() {
    try {
      await this.clearCookies();
    } catch (error) {
      return Promise.reject(error);
    }
  }
}
registerPlugin("CapacitorCookies", {
  web: () => new CapacitorCookiesPluginWeb()
});
const readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {
  const reader = new FileReader();
  reader.onload = () => {
    const base64String = reader.result;
    resolve(base64String.indexOf(",") >= 0 ? base64String.split(",")[1] : base64String);
  };
  reader.onerror = (error) => reject(error);
  reader.readAsDataURL(blob);
});
const normalizeHttpHeaders = (headers = {}) => {
  const originalKeys = Object.keys(headers);
  const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());
  const normalized = loweredKeys.reduce((acc, key, index) => {
    acc[key] = headers[originalKeys[index]];
    return acc;
  }, {});
  return normalized;
};
const buildUrlParams = (params, shouldEncode = true) => {
  if (!params)
    return null;
  const output = Object.entries(params).reduce((accumulator, entry) => {
    const [key, value] = entry;
    let encodedValue;
    let item;
    if (Array.isArray(value)) {
      item = "";
      value.forEach((str) => {
        encodedValue = shouldEncode ? encodeURIComponent(str) : str;
        item += `${key}=${encodedValue}&`;
      });
      item.slice(0, -1);
    } else {
      encodedValue = shouldEncode ? encodeURIComponent(value) : value;
      item = `${key}=${encodedValue}`;
    }
    return `${accumulator}&${item}`;
  }, "");
  return output.substr(1);
};
const buildRequestInit = (options, extra = {}) => {
  const output = Object.assign({ method: options.method || "GET", headers: options.headers }, extra);
  const headers = normalizeHttpHeaders(options.headers);
  const type = headers["content-type"] || "";
  if (typeof options.data === "string") {
    output.body = options.data;
  } else if (type.includes("application/x-www-form-urlencoded")) {
    const params = new URLSearchParams();
    for (const [key, value] of Object.entries(options.data || {})) {
      params.set(key, value);
    }
    output.body = params.toString();
  } else if (type.includes("multipart/form-data") || options.data instanceof FormData) {
    const form = new FormData();
    if (options.data instanceof FormData) {
      options.data.forEach((value, key) => {
        form.append(key, value);
      });
    } else {
      for (const key of Object.keys(options.data)) {
        form.append(key, options.data[key]);
      }
    }
    output.body = form;
    const headers2 = new Headers(output.headers);
    headers2.delete("content-type");
    output.headers = headers2;
  } else if (type.includes("application/json") || typeof options.data === "object") {
    output.body = JSON.stringify(options.data);
  }
  return output;
};
class CapacitorHttpPluginWeb extends WebPlugin {
  /**
   * Perform an Http request given a set of options
   * @param options Options to build the HTTP request
   */
  async request(options) {
    const requestInit = buildRequestInit(options, options.webFetchExtra);
    const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);
    const url = urlParams ? `${options.url}?${urlParams}` : options.url;
    const response = await fetch(url, requestInit);
    const contentType = response.headers.get("content-type") || "";
    let { responseType = "text" } = response.ok ? options : {};
    if (contentType.includes("application/json")) {
      responseType = "json";
    }
    let data;
    let blob;
    switch (responseType) {
      case "arraybuffer":
      case "blob":
        blob = await response.blob();
        data = await readBlobAsBase64(blob);
        break;
      case "json":
        data = await response.json();
        break;
      case "document":
      case "text":
      default:
        data = await response.text();
    }
    const headers = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    return {
      data,
      headers,
      status: response.status,
      url: response.url
    };
  }
  /**
   * Perform an Http GET request given a set of options
   * @param options Options to build the HTTP request
   */
  async get(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "GET" }));
  }
  /**
   * Perform an Http POST request given a set of options
   * @param options Options to build the HTTP request
   */
  async post(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "POST" }));
  }
  /**
   * Perform an Http PUT request given a set of options
   * @param options Options to build the HTTP request
   */
  async put(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "PUT" }));
  }
  /**
   * Perform an Http PATCH request given a set of options
   * @param options Options to build the HTTP request
   */
  async patch(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "PATCH" }));
  }
  /**
   * Perform an Http DELETE request given a set of options
   * @param options Options to build the HTTP request
   */
  async delete(options) {
    return this.request(Object.assign(Object.assign({}, options), { method: "DELETE" }));
  }
}
registerPlugin("CapacitorHttp", {
  web: () => new CapacitorHttpPluginWeb()
});
const scriptRel = "modulepreload";
const assetsURL = function(dep) {
  return "/" + dep;
};
const seen = {};
const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = (cspNonceMeta == null ? void 0 : cspNonceMeta.nonce) || (cspNonceMeta == null ? void 0 : cspNonceMeta.getAttribute("nonce"));
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};
const SplashScreen = registerPlugin("SplashScreen", {
  web: () => __vitePreload(() => import("./web-CBvibSp4.js"), true ? [] : void 0).then((m) => new m.SplashScreenWeb())
});
class SUtils {
  constructor() {
    this.apiKey = localStorage.getItem("openrouter_api_key") || "";
    this.selectedModel = localStorage.getItem("selected_model") || "openai/gpt-4o";
    this.currentTask = "grammar";
    this.isProcessing = false;
    this.theme = localStorage.getItem("theme") || "system";
    this.savedPrompts = JSON.parse(localStorage.getItem("saved_prompts") || "{}");
    this.systemPrompts = {
      chat: "You are a helpful, knowledgeable, and friendly AI assistant. Engage in natural conversation, answer questions, provide explanations, and assist with various tasks. Be conversational, informative, and helpful.",
      grammar: "You are a grammar and writing assistant. Fix grammar, spelling, punctuation, and improve clarity while maintaining the original meaning and tone. Return only the corrected text without explanations.",
      enhance: "You are a writing enhancement assistant. Improve the text by making it more engaging, clear, and well-structured while maintaining the original meaning. Return only the enhanced text.",
      summarize: "You are a summarization assistant. Create a concise, clear summary of the provided text that captures the key points and main ideas. Return only the summary.",
      custom: ""
    };
    this.init();
  }
  async init() {
    await this.initCapacitor();
    this.initTheme();
    this.bindEvents();
    this.loadApiKey();
    this.loadSavedPrompts();
    this.updateUI();
    this.initLucideIcons();
    this.checkApiKeyOnStartup();
  }
  async initCapacitor() {
    try {
      if (Capacitor.isNativePlatform()) {
        await SplashScreen.hide();
        console.log("Capacitor initialized and splash screen hidden");
      } else {
        console.log("Running in web environment");
      }
    } catch (error) {
      console.error("Error initializing Capacitor:", error);
    }
  }
  initTheme() {
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme) {
      this.theme = savedTheme;
    } else {
      this.theme = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
    }
    this.applyTheme();
  }
  applyTheme() {
    document.documentElement.setAttribute("data-theme", this.theme);
    const themeIcon = document.querySelector("#themeToggle i");
    if (themeIcon) {
      themeIcon.setAttribute("data-lucide", this.theme === "dark" ? "sun" : "moon");
      if (window.lucide) {
        window.lucide.createIcons();
      }
    }
  }
  toggleTheme() {
    this.theme = this.theme === "dark" ? "light" : "dark";
    localStorage.setItem("theme", this.theme);
    this.applyTheme();
  }
  initLucideIcons() {
    if (window.lucide) {
      window.lucide.createIcons();
    }
  }
  bindEvents() {
    document.getElementById("themeToggle").addEventListener("click", () => this.toggleTheme());
    document.getElementById("configBtn").addEventListener("click", () => this.openConfigModal());
    document.getElementById("closeModal").addEventListener("click", () => this.closeConfigModal());
    document.getElementById("savePrompt").addEventListener("click", () => this.saveCustomPrompt());
    document.getElementById("loadPrompt").addEventListener("click", () => this.loadCustomPrompt());
    document.getElementById("deletePrompt").addEventListener("click", () => this.deleteCustomPrompt());
    document.getElementById("saveApiKey").addEventListener("click", () => this.saveApiKey());
    document.getElementById("apiKey").addEventListener("keypress", (e) => {
      if (e.key === "Enter") this.saveApiKey();
    });
    document.querySelectorAll(".utility-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const task = e.currentTarget.dataset.task;
        this.selectTask(task);
      });
    });
    document.getElementById("processBtn").addEventListener("click", () => this.processText());
    document.getElementById("clearBtn").addEventListener("click", () => this.clearText());
    document.getElementById("copyBtn").addEventListener("click", () => this.copyOutput());
    document.getElementById("inputText").addEventListener("keypress", (e) => {
      if (e.key === "Enter" && e.ctrlKey) {
        this.processText();
      }
    });
    document.getElementById("configModal").addEventListener("click", (e) => {
      if (e.target === e.currentTarget) {
        this.closeConfigModal();
      }
    });
  }
  loadApiKey() {
    if (this.apiKey) {
      document.getElementById("apiKey").value = this.apiKey;
    }
    document.getElementById("modelSelect").value = this.selectedModel;
  }
  checkApiKeyOnStartup() {
    if (!this.apiKey) {
      this.openConfigModal();
    }
  }
  openConfigModal() {
    const modal = document.getElementById("configModal");
    modal.showModal();
    setTimeout(() => {
      document.getElementById("apiKey").focus();
    }, 100);
  }
  closeConfigModal() {
    const modal = document.getElementById("configModal");
    modal.close();
  }
  loadSavedPrompts() {
    const select = document.getElementById("savedPrompts");
    select.innerHTML = '<option value="">Select a saved prompt...</option>';
    Object.keys(this.savedPrompts).forEach((slug) => {
      const option = document.createElement("option");
      option.value = slug;
      option.textContent = slug;
      select.appendChild(option);
    });
  }
  saveCustomPrompt() {
    const slugInput = document.getElementById("promptSlug");
    const promptInput = document.getElementById("systemPrompt");
    const slug = slugInput.value.trim();
    const prompt = promptInput.value.trim();
    if (!slug) {
      this.showStatus("Please enter a prompt name", "error");
      return;
    }
    if (!prompt) {
      this.showStatus("Please enter a system prompt", "error");
      return;
    }
    if (!/^[a-zA-Z0-9-_]+$/.test(slug)) {
      this.showStatus("Prompt name can only contain letters, numbers, hyphens, and underscores", "error");
      return;
    }
    this.savedPrompts[slug] = prompt;
    localStorage.setItem("saved_prompts", JSON.stringify(this.savedPrompts));
    this.loadSavedPrompts();
    slugInput.value = "";
    promptInput.value = "";
    this.showStatus(`Prompt "${slug}" saved successfully!`, "success");
  }
  loadCustomPrompt() {
    const select = document.getElementById("savedPrompts");
    const selectedSlug = select.value;
    if (!selectedSlug) {
      this.showStatus("Please select a prompt to load", "error");
      return;
    }
    const prompt = this.savedPrompts[selectedSlug];
    if (prompt) {
      document.getElementById("systemPrompt").value = prompt;
      document.getElementById("promptSlug").value = selectedSlug;
      this.showStatus(`Prompt "${selectedSlug}" loaded`, "success");
    } else {
      this.showStatus("Prompt not found", "error");
    }
  }
  deleteCustomPrompt() {
    const select = document.getElementById("savedPrompts");
    const selectedSlug = select.value;
    if (!selectedSlug) {
      this.showStatus("Please select a prompt to delete", "error");
      return;
    }
    if (confirm(`Are you sure you want to delete the prompt "${selectedSlug}"?`)) {
      delete this.savedPrompts[selectedSlug];
      localStorage.setItem("saved_prompts", JSON.stringify(this.savedPrompts));
      this.loadSavedPrompts();
      document.getElementById("promptSlug").value = "";
      document.getElementById("systemPrompt").value = "";
      this.showStatus(`Prompt "${selectedSlug}" deleted`, "success");
    }
  }
  saveApiKey() {
    const apiKeyInput = document.getElementById("apiKey");
    const modelSelect = document.getElementById("modelSelect");
    const apiKey = apiKeyInput.value.trim();
    const selectedModel = modelSelect.value;
    if (!apiKey) {
      this.showStatus("Please enter an API key", "error");
      return;
    }
    if (!selectedModel) {
      this.showStatus("Please select an AI model", "error");
      return;
    }
    this.apiKey = apiKey;
    this.selectedModel = selectedModel;
    localStorage.setItem("openrouter_api_key", apiKey);
    localStorage.setItem("selected_model", selectedModel);
    this.showStatus("Configuration saved successfully!", "success");
    this.updateUI();
    this.closeConfigModal();
  }
  selectTask(task) {
    this.currentTask = task;
    document.querySelectorAll(".utility-btn").forEach((btn) => {
      btn.setAttribute("aria-current", false);
      btn.classList.add("outline");
    });
    document.querySelector(`[data-task="${task}"]`).setAttribute("aria-current", true);
    document.querySelector(`[data-task="${task}"]`).classList.remove("outline");
    if (task === "custom") {
      document.getElementById("systemPrompt").style.display = "block";
    } else {
      document.getElementById("systemPrompt").style.display = "none";
    }
    this.showStatus(`Selected: ${this.getTaskName(task)}`, "info");
  }
  getTaskName(task) {
    const names = {
      chat: "Chat",
      grammar: "Grammar Fix",
      enhance: "Text Enhancement",
      summarize: "Summarization",
      custom: "Custom Agent"
    };
    return names[task] || task;
  }
  async processText() {
    const inputText = document.getElementById("inputText").value.trim();
    if (!inputText) {
      this.showStatus("Please enter some text to process", "error");
      return;
    }
    if (!this.apiKey) {
      this.showStatus("Please set your OpenRouter API key first", "error");
      return;
    }
    if (this.isProcessing) {
      return;
    }
    this.isProcessing = true;
    this.updateUI();
    this.showStatus("Processing...", "info");
    try {
      const result = await this.callOpenRouter(inputText);
      this.renderOutput(result);
      this.showStatus("Processing completed!", "success");
    } catch (error) {
      console.error("Error:", error);
      this.showStatus(`Error: ${error.message}`, "error");
      document.getElementById("outputText").textContent = "An error occurred while processing your request.";
    } finally {
      this.isProcessing = false;
      this.updateUI();
    }
  }
  async callOpenRouter(inputText) {
    var _a;
    let systemPrompt = this.systemPrompts[this.currentTask];
    if (this.currentTask === "custom") {
      systemPrompt = document.getElementById("systemPrompt").value.trim();
      if (!systemPrompt) {
        throw new Error("Please enter a custom system prompt");
      }
    }
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${this.apiKey}`,
        "HTTP-Referer": window.location.origin,
        "X-Title": "SUtils",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        model: this.selectedModel,
        messages: [
          {
            role: "system",
            content: systemPrompt
          },
          {
            role: "user",
            content: inputText
          }
        ],
        temperature: 0.7,
        max_tokens: 2e3
      })
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(((_a = errorData.error) == null ? void 0 : _a.message) || `HTTP ${response.status}: ${response.statusText}`);
    }
    const data = await response.json();
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error("Invalid response format from API");
    }
    return data.choices[0].message.content;
  }
  renderOutput(text) {
    const outputElement = document.getElementById("outputText");
    if (typeof marked !== "undefined") {
      marked.setOptions({
        breaks: true,
        gfm: true,
        sanitize: false,
        // We trust the AI output
        smartLists: true,
        smartypants: true
      });
      outputElement.innerHTML = marked.parse(text);
    } else {
      outputElement.textContent = text;
    }
  }
  clearText() {
    document.getElementById("inputText").value = "";
    document.getElementById("outputText").innerHTML = "Results will appear here...";
    this.showStatus("Text cleared", "info");
  }
  async copyOutput() {
    const outputElement = document.getElementById("outputText");
    const outputText = outputElement.textContent || outputElement.innerText;
    if (outputText === "Results will appear here..." || !outputText.trim()) {
      this.showStatus("No output to copy", "error");
      return;
    }
    try {
      await navigator.clipboard.writeText(outputText);
      this.showStatus("Output copied to clipboard!", "success");
    } catch (error) {
      const textArea = document.createElement("textarea");
      textArea.value = outputText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      this.showStatus("Output copied to clipboard!", "success");
    }
  }
  updateUI() {
    const processBtn = document.getElementById("processBtn");
    const processIcon = document.getElementById("processIcon");
    const processText = document.getElementById("processText");
    const hasApiKey = !!this.apiKey;
    processBtn.disabled = this.isProcessing || !hasApiKey;
    if (this.isProcessing) {
      processIcon.innerHTML = '<div class="loading"></div>';
      processText.textContent = "Processing...";
    } else if (!hasApiKey) {
      processIcon.setAttribute("data-lucide", "alert-triangle");
      processText.textContent = "Set API Key First";
    } else {
      processIcon.setAttribute("data-lucide", "play");
      processText.textContent = "Process Text";
    }
    if (window.lucide) {
      window.lucide.createIcons();
    }
  }
  showStatus(message, type = "info") {
    const statusElement = document.getElementById("status");
    statusElement.textContent = message;
    statusElement.className = type;
    if (type === "success" || type === "info") {
      setTimeout(() => {
        statusElement.textContent = "";
        statusElement.className = "";
      }, 5e3);
    }
  }
}
document.addEventListener("DOMContentLoaded", async () => {
  window.sutils = new SUtils();
  window.sutils.selectTask("chat");
  setTimeout(() => {
    if (window.lucide) {
      window.lucide.createIcons();
    }
  }, 100);
});
export {
  WebPlugin as W
};
