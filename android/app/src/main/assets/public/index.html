<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <title>SUtils</title>
    <meta
      name="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-tap-highlight" content="no" />

    <link rel="icon" type="image/x-icon" href="/assets/favicon-UvKAN7pv.ico" />
    <link rel="manifest" href="/assets/manifest-RD-sWVQA.json" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.orange.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <meta name="theme-color" content="#000000" />
    <script type="module" crossorigin src="/assets/index-DyaqUIKw.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-5cHucQ3C.css">
  </head>
  <body>
    <main class="container">
      <header>
        <nav>
  <ul>
    <li><strong>
    <h3>
      S Utils
    </h3>
    </strong></li>
  </ul>
  <ul>
            <li>
              <button id="configBtn" class="outline secondary" aria-label="Configure API Key">
                <i data-lucide="settings"></i>
              </button>
            </li>
            <li>
              <button id="themeToggle" class="outline secondary" aria-label="Toggle theme">
                <i data-lucide="moon"></i>
              </button>
            </li>
          </ul>
</nav>
      </header>

      <!-- Configuration Modal -->
      <dialog id="configModal">
        <article>
          <header>
            <button id="closeModal" aria-label="Close" rel="prev"></button>
            <p>
              <strong>Configuration</strong>
            </p>
          </header>
          <main>
            <label for="apiKey">
              OpenRouter API Key
              <input type="password" id="apiKey" placeholder="sk-or-v1-..." />
            </label>
            <small>
              Get your API key from <a href="https://openrouter.ai/keys" target="_blank">OpenRouter</a>
            </small>

            <label for="modelSelect">
              AI Model
              <select id="modelSelect" aria-label="Select AI model..." required>
                <option selected disabled value="">
                  Select AI model...
                </option>
                <option value="google/gemma-3-4b-it:free">Gemma 3 4b (Google)</option>
                <option value="mistralai/devstral-small-2505:free">Devstral Small 2505 (Mistral)</option>
                <option value="qwen/qwen3-4b:free">Qwen3 4B (Qwen)</option>
                <option value="openai/gpt-4.1-nano">GPT-4.1 Nano (OpenAI)</option>
                <option value="openai/gpt-4o-mini">GPT-4o Mini (OpenAI)</option>
                <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet (Anthropic)</option>
                <option value="anthropic/claude-3-haiku">Claude 3 Haiku (Anthropic)</option>
                <option value="google/gemini-pro-1.5">Gemini Pro 1.5 (Google)</option>
                <option value="meta-llama/llama-3.1-8b-instruct">Llama 3.1 8B (Meta)</option>
                <option value="meta-llama/llama-3.1-70b-instruct">Llama 3.1 70B (Meta)</option>
                <option value="mistralai/mistral-7b-instruct">Mistral 7B (Mistral AI)</option>
                <option value="cohere/command-r-plus">Command R+ (Cohere)</option>
              </select>
            </label>
            <small>
              Choose the AI model for text processing. Different models have varying capabilities and costs.
            </small>

            <button style="width:100%; margin-top: 1.2rem" id="saveApiKey">Save Configuration</button>
          </main>
        </article>
      </dialog>

      <section>
        <h3>Quick Utilities</h3>
        <div role="group">
          <button class="outline utility-btn" data-task="chat">
            <i data-lucide="message-circle"></i>
            Chat
          </button>
          <button class="outline utility-btn" data-task="grammar">
            <i data-lucide="spell-check"></i>
            Grammar
          </button>
          <button class="outline utility-btn" data-task="enhance">
            <i data-lucide="sparkles"></i>
            Enhance
          </button>
          <button class="outline utility-btn" data-task="summarize">
            <i data-lucide="file-text"></i>
            Summarize
          </button>
        </div>
      </section>

      <details>
        <summary>Custom Prompts</summary>
        <div class="grid">
          <label for="savedPrompts">
            Saved Prompts
            <select id="savedPrompts" aria-label="Select saved prompt...">
              <option value="">Select a saved prompt...</option>
            </select>
          </label>
          <button id="loadPrompt" class="outline">Load</button>
        </div>

        <label for="promptSlug">
          Prompt Name
          <input type="text" id="promptSlug" placeholder="e.g., code-reviewer, translator..." />
        </label>

        <label for="systemPrompt">
          System Prompt
          <textarea id="systemPrompt" placeholder="You are a helpful assistant that..." rows="4"></textarea>
        </label>

        <div class="grid">
          <button id="savePrompt" class="outline">Save Prompt</button>
          <button id="deletePrompt" class="outline secondary">Delete</button>
        </div>
      </details>

      <div class="grid">
        <section>
          <h3>Input</h3>
          <textarea id="inputText" placeholder="Enter your text here..." rows="8"></textarea>
          <div class="grid">
            <button id="processBtn">
              <i id="processIcon" data-lucide="play"></i>
              <span id="processText">Process Text</span>
            </button>
            <button id="clearBtn" class="outline">
              <i data-lucide="x"></i>
              Clear
            </button>
          </div>
        </section>

        <section>
          <h3>Output</h3>
          <div id="outputText" class="output-area">Results will appear here...</div>
          <button id="copyBtn" class="outline" style="width:100%;">
            <i data-lucide="copy"></i>
            Copy
          </button>
        </section>
      </div>

      <section>
        <div id="status" role="status" aria-live="polite"></div>
      </section>
    </main>

  </body>
</html>
