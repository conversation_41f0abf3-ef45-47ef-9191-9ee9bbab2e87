me.soham901.utils.app-startup-runtime-1.1.1-0 /home/<USER>/.gradle/caches/8.11.1/transforms/0c35e3b0c2fe34519a603108fedf6f64/transformed/startup-runtime-1.1.1/res
me.soham901.utils.app-profileinstaller-1.3.1-1 /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/res
me.soham901.utils.app-appcompat-resources-1.7.0-2 /home/<USER>/.gradle/caches/8.11.1/transforms/16884767c054ac4cab0f70a5a4855d4d/transformed/appcompat-resources-1.7.0/res
me.soham901.utils.app-lifecycle-process-2.6.2-3 /home/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/res
me.soham901.utils.app-webkit-1.12.1-4 /home/<USER>/.gradle/caches/8.11.1/transforms/3fbbd497413c0ee4069f169f4dc7a260/transformed/webkit-1.12.1/res
me.soham901.utils.app-emoji2-1.3.0-5 /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/res
me.soham901.utils.app-lifecycle-viewmodel-savedstate-2.6.2-6 /home/<USER>/.gradle/caches/8.11.1/transforms/4b3e930a763ca268d3b38830b586bc2a/transformed/lifecycle-viewmodel-savedstate-2.6.2/res
me.soham901.utils.app-lifecycle-viewmodel-2.6.2-7 /home/<USER>/.gradle/caches/8.11.1/transforms/5a75dca28172537968edb11f4713fc67/transformed/lifecycle-viewmodel-2.6.2/res
me.soham901.utils.app-constraintlayout-2.0.1-8 /home/<USER>/.gradle/caches/8.11.1/transforms/5ca5320cafc5c15decacd9f0c2e7b059/transformed/constraintlayout-2.0.1/res
me.soham901.utils.app-viewpager2-1.0.0-9 /home/<USER>/.gradle/caches/8.11.1/transforms/5d9154e2b2a28df5f8c34d2f639641a9/transformed/viewpager2-1.0.0/res
me.soham901.utils.app-core-1.15.0-10 /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/res
me.soham901.utils.app-transition-1.5.0-11 /home/<USER>/.gradle/caches/8.11.1/transforms/5fcb41078acc11b974520eb0257b4c84/transformed/transition-1.5.0/res
me.soham901.utils.app-tracing-1.2.0-12 /home/<USER>/.gradle/caches/8.11.1/transforms/640934fa0c2deb02232fb188bbe4540c/transformed/tracing-1.2.0/res
me.soham901.utils.app-coordinatorlayout-1.2.0-13 /home/<USER>/.gradle/caches/8.11.1/transforms/75881b531e34911967ea794bd3408c30/transformed/coordinatorlayout-1.2.0/res
me.soham901.utils.app-fragment-1.8.4-14 /home/<USER>/.gradle/caches/8.11.1/transforms/7a4193c6fbbe5e128015b7f6283124c0/transformed/fragment-1.8.4/res
me.soham901.utils.app-lifecycle-livedata-core-2.6.2-15 /home/<USER>/.gradle/caches/8.11.1/transforms/7cb5e40cf228b6c33a81f0245b3675c9/transformed/lifecycle-livedata-core-2.6.2/res
me.soham901.utils.app-core-runtime-2.2.0-16 /home/<USER>/.gradle/caches/8.11.1/transforms/8f584d735ff9bfd7953105e9d7907cfc/transformed/core-runtime-2.2.0/res
me.soham901.utils.app-savedstate-1.2.1-17 /home/<USER>/.gradle/caches/8.11.1/transforms/998c918bf96ae2f6a4f5c8c644413a6f/transformed/savedstate-1.2.1/res
me.soham901.utils.app-material-1.12.0-18 /home/<USER>/.gradle/caches/8.11.1/transforms/a63b4def3f126a1584608cf719a55f7b/transformed/material-1.12.0/res
me.soham901.utils.app-activity-1.9.2-19 /home/<USER>/.gradle/caches/8.11.1/transforms/b5af023aa879967c1b3537c24628d5a4/transformed/activity-1.9.2/res
me.soham901.utils.app-annotation-experimental-1.4.1-20 /home/<USER>/.gradle/caches/8.11.1/transforms/c7519e99783a8412739804580db52b13/transformed/annotation-experimental-1.4.1/res
me.soham901.utils.app-recyclerview-1.1.0-21 /home/<USER>/.gradle/caches/8.11.1/transforms/cc6978855835d803573ad2e08c535bfb/transformed/recyclerview-1.1.0/res
me.soham901.utils.app-lifecycle-livedata-2.6.2-22 /home/<USER>/.gradle/caches/8.11.1/transforms/d68bb0f6b9dc7d41a2b6dfc3b4acad60/transformed/lifecycle-livedata-2.6.2/res
me.soham901.utils.app-appcompat-1.7.0-23 /home/<USER>/.gradle/caches/8.11.1/transforms/dda665aa4a1576cfb1759fb2bbcd5279/transformed/appcompat-1.7.0/res
me.soham901.utils.app-core-ktx-1.15.0-24 /home/<USER>/.gradle/caches/8.11.1/transforms/eaa603a2c27a5645906131bea5891cf1/transformed/core-ktx-1.15.0/res
me.soham901.utils.app-core-splashscreen-1.0.1-25 /home/<USER>/.gradle/caches/8.11.1/transforms/ef234481c09f01fb9f0508a5da2b1126/transformed/core-splashscreen-1.0.1/res
me.soham901.utils.app-lifecycle-runtime-2.6.2-26 /home/<USER>/.gradle/caches/8.11.1/transforms/f212eb2fcec7b76a8049b85cea08416b/transformed/lifecycle-runtime-2.6.2/res
me.soham901.utils.app-drawerlayout-1.1.1-27 /home/<USER>/.gradle/caches/8.11.1/transforms/f43ce8c15a68d2ab8c4f562154cdbd48/transformed/drawerlayout-1.1.1/res
me.soham901.utils.app-emoji2-views-helper-1.3.0-28 /home/<USER>/.gradle/caches/8.11.1/transforms/fd457bbc543ecdbee5c91b5aa5e11e77/transformed/emoji2-views-helper-1.3.0/res
me.soham901.utils.app-cardview-1.0.0-29 /home/<USER>/.gradle/caches/8.11.1/transforms/ffb3076b6910ef8ba5c42525a0005009/transformed/cardview-1.0.0/res
me.soham901.utils.app-pngs-30 /home/<USER>/soham901/sutils/android/app/build/generated/res/pngs/debug
me.soham901.utils.app-resValues-31 /home/<USER>/soham901/sutils/android/app/build/generated/res/resValues/debug
me.soham901.utils.app-packageDebugResources-32 /home/<USER>/soham901/sutils/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
me.soham901.utils.app-packageDebugResources-33 /home/<USER>/soham901/sutils/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
me.soham901.utils.app-debug-34 /home/<USER>/soham901/sutils/android/app/build/intermediates/merged_res/debug/mergeDebugResources
me.soham901.utils.app-debug-35 /home/<USER>/soham901/sutils/android/app/src/debug/res
me.soham901.utils.app-main-36 /home/<USER>/soham901/sutils/android/app/src/main/res
me.soham901.utils.app-debug-37 /home/<USER>/soham901/sutils/android/capacitor-cordova-android-plugins/build/intermediates/packaged_res/debug/packageDebugResources
me.soham901.utils.app-debug-38 /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+android@7.4.2_@capacitor+core@7.4.2/node_modules/@capacitor/android/capacitor/build/intermediates/packaged_res/debug/packageDebugResources
me.soham901.utils.app-debug-39 /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/packaged_res/debug/packageDebugResources
me.soham901.utils.app-debug-40 /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+splash-screen@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/splash-screen/android/build/intermediates/packaged_res/debug/packageDebugResources
