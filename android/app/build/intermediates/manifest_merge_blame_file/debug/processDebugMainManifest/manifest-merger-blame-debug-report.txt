1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.soham901.utils"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:40:5-67
13-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:40:22-64
14
15    <queries>
15-->[:capacitor-camera] /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
16        <intent>
16-->[:capacitor-camera] /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
17            <action android:name="android.media.action.IMAGE_CAPTURE" />
17-->[:capacitor-camera] /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-73
17-->[:capacitor-camera] /home/<USER>/soham901/sutils/node_modules/.pnpm/@capacitor+camera@7.0.1_@capacitor+core@7.4.2/node_modules/@capacitor/camera/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-70
18        </intent>
19    </queries>
20
21    <permission
21-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:22:5-24:47
22        android:name="me.soham901.utils.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
22-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:23:9-81
23        android:protectionLevel="signature" />
23-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:24:9-44
24
25    <uses-permission android:name="me.soham901.utils.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
25-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:5-97
25-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:26:22-94
26
27    <application
27-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:4:5-36:19
28        android:allowBackup="true"
28-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:5:9-35
29        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
29-->[androidx.core:core:1.15.0] /home/<USER>/.gradle/caches/8.11.1/transforms/5f51ed623ec66baebfa6a053fe8a8b2a/transformed/core-1.15.0/AndroidManifest.xml:28:18-86
30        android:debuggable="true"
31        android:extractNativeLibs="false"
32        android:icon="@mipmap/ic_launcher"
32-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:6:9-43
33        android:label="@string/app_name"
33-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:7:9-41
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:8:9-54
35        android:supportsRtl="true"
35-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:9:9-35
36        android:theme="@style/AppTheme" >
36-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:10:9-40
37        <activity
37-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:12:9-25:20
38            android:name="me.soham901.utils.MainActivity"
38-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:14:13-41
39            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
39-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:13:13-140
40            android:exported="true"
40-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:18:13-36
41            android:label="@string/title_activity_main"
41-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:15:13-56
42            android:launchMode="singleTask"
42-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:17:13-44
43            android:theme="@style/AppTheme.NoActionBarLaunch" >
43-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:16:13-62
44            <intent-filter>
44-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:20:13-23:29
45                <action android:name="android.intent.action.MAIN" />
45-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:21:17-69
45-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:21:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:22:17-77
47-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:22:27-74
48            </intent-filter>
49        </activity>
50
51        <provider
52            android:name="androidx.core.content.FileProvider"
52-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:28:13-62
53            android:authorities="me.soham901.utils.fileprovider"
53-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:29:13-64
54            android:exported="false"
54-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:30:13-37
55            android:grantUriPermissions="true" >
55-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:31:13-47
56            <meta-data
56-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:32:13-34:64
57                android:name="android.support.FILE_PROVIDER_PATHS"
57-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:33:17-67
58                android:resource="@xml/file_paths" />
58-->/home/<USER>/soham901/sutils/android/app/src/main/AndroidManifest.xml:34:17-51
59        </provider>
60        <provider
60-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:24:9-32:20
61            android:name="androidx.startup.InitializationProvider"
61-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:25:13-67
62            android:authorities="me.soham901.utils.androidx-startup"
62-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:26:13-68
63            android:exported="false" >
63-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:27:13-37
64            <meta-data
64-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:29:13-31:52
65                android:name="androidx.emoji2.text.EmojiCompatInitializer"
65-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:30:17-75
66                android:value="androidx.startup" />
66-->[androidx.emoji2:emoji2:1.3.0] /home/<USER>/.gradle/caches/8.11.1/transforms/455628e39230ce08b70f281b4ac99c3c/transformed/emoji2-1.3.0/AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.lifecycle:lifecycle-process:2.6.2] /home/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:29:13-31:52
68                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
68-->[androidx.lifecycle:lifecycle-process:2.6.2] /home/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:30:17-78
69                android:value="androidx.startup" />
69-->[androidx.lifecycle:lifecycle-process:2.6.2] /home/<USER>/.gradle/caches/8.11.1/transforms/2086035d7e747a32c2be40e6ed7f404e/transformed/lifecycle-process-2.6.2/AndroidManifest.xml:31:17-49
70            <meta-data
70-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
71                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
72                android:value="androidx.startup" />
72-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
73        </provider>
74
75        <receiver
75-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
76            android:name="androidx.profileinstaller.ProfileInstallReceiver"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
77            android:directBootAware="false"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
78            android:enabled="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
79            android:exported="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
80            android:permission="android.permission.DUMP" >
80-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
82                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
82-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
85                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
85-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
88                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
88-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
91                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
91-->[androidx.profileinstaller:profileinstaller:1.3.1] /home/<USER>/.gradle/caches/8.11.1/transforms/0f3d68271fd414f74f11a6a9c308b287/transformed/profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
92            </intent-filter>
93        </receiver>
94    </application>
95
96</manifest>
