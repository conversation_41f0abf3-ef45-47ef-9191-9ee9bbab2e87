/* Custom styles on top of Pico CSS */

/* Mobile-first responsive design */
.container {
  padding: 0.5rem;
  max-width: 100%;
}

@media (min-width: 576px) {
  .container {
    padding: 1rem;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* Button grid styling for equal widths */
.grid button {
  flex: 1;
  min-width: 0; /* Prevent flex items from overflowing */
}

/* API Key input styling */
.api-key-input {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.api-key-input input {
  flex: 1;
  margin-bottom: 0;
}

.api-key-input button {
  margin-bottom: 0;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  white-space: nowrap;
}

.output-area h1,
.output-area h2,
.output-area h3,
.output-area h4,
.output-area h5,
.output-area h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.output-area h1 { font-size: 1.5rem; }
.output-area h2 { font-size: 1.25rem; }
.output-area h3 { font-size: 1.125rem; }
.output-area h4 { font-size: 1rem; }
.output-area h5 { font-size: 0.875rem; }
.output-area h6 { font-size: 0.75rem; }

.output-area p {
  margin-bottom: 1rem;
}

.output-area ul,
.output-area ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.output-area li {
  margin-bottom: 0.25rem;
}

.output-area blockquote {
  margin: 1rem 0;
  padding: 0.5rem 1rem;
  border-left: 4px solid var(--pico-primary-background);
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--pico-color);
  font-style: italic;
  border-radius: 0.25rem;
}

[data-theme="dark"] .output-area blockquote {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--pico-color);
}

.output-area code {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--pico-color);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: var(--pico-font-family-monospace);
  font-size: 0.8rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .output-area code {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--pico-color);
}

.output-area pre {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--pico-color);
  padding: 1rem;
  border-radius: var(--pico-border-radius);
  overflow-x: auto;
  margin: 1rem 0;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .output-area pre {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--pico-color);
}

.output-area pre code {
  background: none;
  padding: 0;
  border: none;
}

.output-area table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.output-area th,
.output-area td {
  padding: 0.5rem;
  border: 1px solid var(--pico-border-color);
  text-align: left;
}

.output-area th {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--pico-color);
  font-weight: 600;
}

[data-theme="dark"] .output-area th {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--pico-color);
}

.output-area strong {
  font-weight: 600;
}

.output-area em {
  font-style: italic;
}

.output-area a {
  color: var(--pico-primary-background);
  text-decoration: underline;
}

.output-area hr {
  margin: 1.5rem 0;
  border: none;
  border-top: 1px solid var(--pico-border-color);
}

/* Button icon spacing */
button i {
  width: 1rem;
  height: 1rem;
}

/* Status message styling */
#status {
  padding: 0.75rem 1rem;
  border-radius: var(--pico-border-radius);
  font-weight: 500;
  text-align: center;
  margin-top: 1rem;
  transition: all 0.2s ease-in-out;
}

#status.success {
  background-color: var(--pico-ins-color);
  color: var(--pico-contrast-inverse);
}

#status.error {
  background-color: var(--pico-del-color);
  color: var(--pico-contrast-inverse);
}

#status.info {
  background-color: var(--pico-primary-background);
  color: var(--pico-primary-inverse);
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--pico-muted-border-color);
  border-radius: 50%;
  border-top-color: var(--pico-primary-background);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Mobile-specific improvements */

/* Header navigation improvements */
nav {
  padding: 0.5rem 0;
}

nav ul {
  margin: 0;
  padding: 0;
}

nav button {
  padding: 0.5rem;
  min-width: 44px; /* Touch target size */
  min-height: 44px;
}

/* Utility buttons mobile layout */
.utility-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin: 1rem 0;
}

@media (min-width: 576px) {
  .utility-buttons {
    grid-template-columns: repeat(4, 1fr);
  }
}

.utility-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem 0.5rem;
  min-height: 80px;
  text-align: center;
  font-size: 0.875rem;
  gap: 0.5rem;
}

.utility-btn i {
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
}

/* Mobile form improvements */
@media (max-width: 767px) {
  /* Stack form elements on mobile */
  .grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Make buttons full width on mobile */
  .grid button {
    width: 100%;
  }

  /* Improve textarea sizing */
  textarea {
    min-height: 120px;
    resize: vertical;
  }

  /* Modal improvements for mobile */
  dialog {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }

  dialog article {
    margin: 0;
  }

  /* Improve input sizing */
  input, select, textarea {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Tablet and desktop layout */
@media (min-width: 768px) {
  .main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .input-section {
    grid-column: 1;
  }

  .output-section {
    grid-column: 2;
  }

  .utility-buttons {
    grid-column: 1 / -1;
  }

  .custom-prompts {
    grid-column: 1 / -1;
  }
}

/* Improve touch targets */
button, input, select, textarea {
  min-height: 44px;
}

/* Action buttons styling */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.75rem;
  margin-top: 1rem;
}

@media (max-width: 576px) {
  .action-buttons {
    grid-template-columns: 1fr;
  }
}

.primary-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
}

.secondary-action {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  min-width: 120px;
}

.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  margin-top: 1rem;
  padding: 0.75rem;
}

/* Custom prompts styling */
.custom-prompts {
  margin: 1.5rem 0;
}

.custom-prompts summary {
  padding: 0.75rem;
  background-color: var(--pico-card-background-color);
  border: var(--pico-border-width) solid var(--pico-border-color);
  border-radius: var(--pico-border-radius);
  cursor: pointer;
  font-weight: 600;
  margin-bottom: 1rem;
}

.custom-prompts[open] summary {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
  margin-bottom: 0;
}

.prompt-controls {
  padding: 1rem;
  background-color: var(--pico-card-background-color);
  border: var(--pico-border-width) solid var(--pico-border-color);
  border-top: none;
  border-radius: 0 0 var(--pico-border-radius) var(--pico-border-radius);
}

.prompt-controls > * {
  margin-bottom: 1rem;
}

.prompt-controls > *:last-child {
  margin-bottom: 0;
}

/* Better spacing for mobile */
section {
  margin-bottom: 1.5rem;
}

@media (max-width: 767px) {
  section {
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
  }

  .custom-prompts {
    margin: 1rem 0;
  }

  .prompt-controls {
    padding: 0.75rem;
  }
}
